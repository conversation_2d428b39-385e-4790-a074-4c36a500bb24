# AiLex Legal Assistant

A modern legal assistant application built with React, TypeScript, and Express.

## Development Setup

This project includes automated code quality gates:
- **Pre-commit hooks**: Prettier formatting and TypeScript checking
- **GitHub Actions CI**: Lint, typecheck, build verification, and security audits

## Available Scripts

- `pnpm run dev` - Start development server
- `pnpm run build` - Build for production
- `pnpm run typecheck` - Run TypeScript type checking
- `pnpm run lint` - Run ESLint
- `pnpm run format` - Format code with Prettier
- `pnpm run format:check` - Check code formatting
